
import { z } from "zod";

export const incidentFormSchema = z.object({
  // Step 1 - Incident Information
  incidentTitle: z.string(),
  incidentDate: z.date(),
  incidentTime: z.string(),
  incidentType: z.string(),

  // Location information
  locationCountry: z.string(),
  locationCity: z.string(),
  locationBusinessUnit: z.string(),
  locationProject: z.string(),
  locationDetails: z.string(),

  // Classification of Actual Injury
  isWorkRelated: z.boolean().nullable(),
  lossOfConsciousness: z.boolean().nullable(),
  isDangerousOccurrence: z.boolean().nullable(),
  injuryClassification: z.object({
    isFatality: z.boolean().nullable(),
    isPermanentDisability: z.boolean().nullable(),
    isLostTimeIncident: z.boolean().nullable(),
    isMedicalTreatment: z.boolean().nullable(),
    isFirstAid: z.boolean().nullable(),
  }),

  // Incident Reviewer
  incidentReviewer: z.string(),

  // Optional fields from original form
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),
  incidentCategory: z.string().optional(),
  circumstances: z.string().optional(),
  workplaceActivity: z.string().optional(),
  riskCategories: z.array(z.string()).optional(),
  photos: z.array(z.any()).optional(),
  impactAssessment: z.object({
    injury: z.string().optional(),
    environmentalDamage: z.string().optional(),
    productionLoss: z.string().optional(),
    reputationalDamage: z.string().optional(),
  }).optional(),

  reportToAuthorities: z.boolean().default(false),
  authorityReportDetails: z.string().optional(),

  // Final confirmation
  confirmAccuracy: z.boolean().refine(val => val === true),
});

export type IncidentFormValues = z.infer<typeof incidentFormSchema>;
