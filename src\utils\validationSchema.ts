
import { z } from "zod";

export const incidentFormSchema = z.object({
  // Step 1 - Incident Information
  incidentTitle: z.string().min(1),
  incidentDate: z.date(),
  incidentTime: z.string().min(1),
  incidentType: z.string().min(1),

  // Location information
  locationCountry: z.string().min(1),
  locationCity: z.string().min(1),
  locationBusinessUnit: z.string().min(1),
  locationProject: z.string().min(1),
  locationDetails: z.string().min(1),

  // Classification of Actual Injury
  isWorkRelated: z.boolean().nullable(),
  lossOfConsciousness: z.boolean().nullable(),
  isDangerousOccurrence: z.boolean().nullable(),
  injuryClassification: z.object({
    isFatality: z.boolean().nullable(),
    isPermanentDisability: z.boolean().nullable(),
    isLostTimeIncident: z.boolean().nullable(),
    isMedicalTreatment: z.boolean().nullable(),
    isFirstAid: z.boolean().nullable(),
  }),

  // Incident Reviewer
  incidentReviewer: z.string().min(1),

  // Optional fields from original form
  propertyDamage: z.boolean().default(false),
  propertyDamageDetails: z.string().optional(),
  incidentCategory: z.string().optional(),
  circumstances: z.string().optional(),
  workplaceActivity: z.string().optional(),
  riskCategories: z.array(z.string()).optional(),
  photos: z.array(z.any()).optional(),
  impactAssessment: z.object({
    injury: z.string().optional(),
    environmentalDamage: z.string().optional(),
    productionLoss: z.string().optional(),
    reputationalDamage: z.string().optional(),
  }).optional(),

  reportToAuthorities: z.boolean().default(false),
  authorityReportDetails: z.string().optional(),

  // Final confirmation
  confirmAccuracy: z.boolean().refine(val => val === true),
});

export type IncidentFormValues = z.infer<typeof incidentFormSchema>;
