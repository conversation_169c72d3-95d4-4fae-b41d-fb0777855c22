import React from "react";
import { cn } from "@/lib/utils";

interface WorkflowStepIndicatorProps {
  currentStage: 'initial' | 'review' | 'investigation';
  userRole: 'reporter' | 'reviewer' | 'owner';
  isInitialReportCompleted?: boolean;
  onStepClick?: (stage: 'initial' | 'review' | 'investigation') => void;
}

const WorkflowStepIndicator: React.FC<WorkflowStepIndicatorProps> = ({
  currentStage,
  userRole,
  isInitialReportCompleted = false,
  onStepClick,
}) => {
  // Define the workflow steps
  const workflowSteps = [
    { id: 'initial', title: "Initial Report", number: 1 },
    { id: 'review', title: "Reviewer", number: 2 },
    { id: 'investigation', title: "Preliminary Investigation & Analysis", number: 3 },
  ];

  // Determine which step is active and which are completed
  const getStepStatus = (stepId: string) => {
    const stages = ['initial', 'review', 'investigation'];
    const currentIndex = stages.indexOf(currentStage);
    const stepIndex = stages.indexOf(stepId);

    // Special case for initial report
    if (stepId === 'initial' && isInitialReportCompleted) {
      return 'completed';
    }

    if (stepIndex < currentIndex) {
      return 'completed';
    } else if (stepIndex === currentIndex) {
      return 'active';
    } else {
      return 'upcoming';
    }
  };

  // Determine if the current user can edit this step
  const canEditStep = (stepId: string) => {
    if (userRole === 'reporter' && stepId === 'initial') {
      return true;
    } else if (userRole === 'reviewer' && (stepId === 'initial' || stepId === 'review')) {
      return true;
    } else if (userRole === 'owner') {
      // Allow Incident Owner to edit all stages (Initial, Review, and Investigation)
      return true;
    }
    return false;
  };

  return (
    <div className="w-full mb-6">
      {/* Mobile View - Simple Step X of Y */}
      <div className="md:hidden mb-2">
        <p className="text-sm font-medium text-muted-foreground">
          Current Stage: {workflowSteps.find(step => step.id === currentStage)?.title}
        </p>
        <div className="mt-2 h-2 bg-muted rounded-full overflow-hidden">
          <div
            className={cn(
              "h-full transition-all duration-500 ease-in-out rounded-full",
              currentStage === 'initial' ? "bg-primary" : "bg-green-500"
            )}
            style={{
              width: `${
                currentStage === 'initial' ? 33.33 :
                currentStage === 'review' ? 66.66 :
                100
              }%`
            }}
          ></div>
        </div>
      </div>

      {/* Desktop View - Horizontal Steps with connecting lines */}
      <div className="hidden md:block">
        <div className="relative flex items-center justify-between">
          {/* Connecting lines in the background */}
          <div className="absolute left-0 right-0 top-1/2 h-0.5 bg-muted -translate-y-1/2"></div>

          {/* Green progress line for completed steps */}
          {currentStage !== 'initial' && (
            <div
              className="absolute left-0 top-1/2 h-0.5 bg-green-500 -translate-y-1/2"
              style={{
                width: currentStage === 'review' ? '50%' : '100%'
              }}
            ></div>
          )}

          {workflowSteps.map((step, index) => {
            const status = getStepStatus(step.id);
            const isActive = status === 'active';
            const isCompleted = status === 'completed';
            const isEditable = canEditStep(step.id);

            return (
              <div
                key={step.id}
                className={cn(
                  "relative flex flex-col items-center gap-2 z-10",
                  isEditable && onStepClick ? "cursor-pointer" : ""
                )}
                onClick={() => {
                  // Only allow clicking if the step is editable and onStepClick is provided
                  if (isEditable && onStepClick) {
                    onStepClick(step.id as 'initial' | 'review' | 'investigation');
                  }
                }}
              >
                <div
                  className={cn(
                    "flex h-10 w-10 items-center justify-center rounded-full border-2 transition-colors",
                    isActive
                      ? "border-primary bg-primary text-primary-foreground"
                      : isCompleted
                      ? "border-green-500 bg-green-500 text-white"
                      : "border-muted bg-background text-muted-foreground"
                  )}
                >
                  <span className="text-sm font-medium">{step.number}</span>
                </div>
                <span
                  className={cn(
                    "text-sm font-medium",
                    isActive ? "text-primary" :
                    isCompleted ? "text-green-600" :
                    "text-muted-foreground/50"
                  )}
                >
                  {step.title}
                </span>

              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default WorkflowStepIndicator;
