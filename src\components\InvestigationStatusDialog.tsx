import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { leadInvestigators } from "@/utils/formData";
import { ClipboardList, User } from "lucide-react";

interface InvestigationStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incident: any | null;
  onSave: (data: { leadInvestigator: string; remarks: string }) => void;
  onStartInvestigation?: (data: { leadInvestigator: string; remarks: string }) => void;
}

const InvestigationStatusDialog: React.FC<InvestigationStatusDialogProps> = ({
  open,
  onOpenChange,
  incident,
  onSave,
  onStartInvestigation,
}) => {
  const [leadInvestigator, setLeadInvestigator] = useState<string>("");
  const [remarks, setRemarks] = useState<string>("");

  // Reset form when dialog opens
  React.useEffect(() => {
    if (open) {
      setLeadInvestigator(incident?.leadInvestigator || "");
      setRemarks(incident?.investigationRemarks || "");
    }
  }, [open, incident]);

  const handleSave = () => {
    if (!leadInvestigator.trim()) {
      return; // Don't save if no lead investigator is selected
    }

    onSave({
      leadInvestigator,
      remarks: remarks.trim(),
    });

    onOpenChange(false);
  };

  const handleStartInvestigation = () => {
    if (!leadInvestigator.trim()) {
      return; // Don't start if no lead investigator is selected
    }

    if (onStartInvestigation) {
      onStartInvestigation({
        leadInvestigator,
        remarks: remarks.trim(),
      });
    }

    onOpenChange(false);
  };

  const handleCancel = () => {
    setLeadInvestigator("");
    setRemarks("");
    onOpenChange(false);
  };

  if (!incident) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md w-[90vw]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5 text-blue-600" />
            Investigation Assignment
          </DialogTitle>
          <DialogDescription>
            Assign a lead investigator for incident {incident.id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Lead Investigator Selection */}
          <div className="space-y-2">
            <Label htmlFor="leadInvestigator" className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Lead Investigator *
            </Label>
            <Select value={leadInvestigator} onValueChange={setLeadInvestigator}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a lead investigator" />
              </SelectTrigger>
              <SelectContent>
                {leadInvestigators.map((investigator) => (
                  <SelectItem key={investigator.value} value={investigator.value}>
                    {investigator.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Remarks Text Box */}
          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-sm font-medium">
              Remarks
            </Label>
            <Textarea
              id="remarks"
              placeholder="Add any additional remarks or instructions for the investigation..."
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={500}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Optional field for additional context</span>
              <span>{remarks.length}/500 characters</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!leadInvestigator.trim()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Save Assignment
          </Button>
          {onStartInvestigation && (
            <Button
              onClick={handleStartInvestigation}
              disabled={!leadInvestigator.trim()}
              className="bg-green-600 hover:bg-green-700"
            >
              Submit & Start Investigation
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvestigationStatusDialog;
